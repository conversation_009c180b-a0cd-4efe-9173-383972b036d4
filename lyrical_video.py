import json
import subprocess
import shlex
import textwrap
import re
import os


def normalize_lyrics(text: str, max_chars: int = 25) -> list:
    """
    Wrap text to max_chars per line, preserving word boundaries and indentation.
    Escapes special characters for FFmpeg drawtext compatibility.
    Returns a list of lines suitable for ffmpeg drawtext.
    """
    # Escape special characters for FFmpeg
    text = text.replace("'", r"‘")  # Single quote
    text = text.replace(":", r"\:")  # Colon
    text = text.replace(",", r"\,")  # Comma
    text = text.replace("%", r"\%")  # Percent
    text = text.replace("@", r"\@")  # At symbol
    indent_match = re.match(r"\s*", text)
    indent = indent_match.group(0) if indent_match else ""
    stripped = text.strip()
    wrapper = textwrap.TextWrapper(
        width=max_chars,
        initial_indent=indent,
        subsequent_indent=indent,
        break_long_words=False,
        break_on_hyphens=False,
    )
    lines = wrapper.wrap(stripped)
    return lines


def ffprobe_duration(path):
    """Return duration in seconds (float) of an audio/video file via ffprobe."""
    cmd = [
        "ffprobe",
        "-v",
        "error",
        "-show_entries",
        "format=duration",
        "-of",
        "default=noprint_wrappers=1:nokey=1",
        path,
    ]
    out = subprocess.check_output(cmd).strip()
    return float(out)


def parse_time(ts):
    """
    Parse timestamps like "0:08:219" (m:ss:ms) into seconds (float).
    Assumes ms is 3-digit milliseconds.
    """
    m, s, ms = ts.split(":")
    return int(m) * 60 + int(s) + int(ms) / 1000.0


def create_lyric_video(
    audio_path: str,
    user_image_path: str,
    border_path: str,
    brand_logo_path: str,
    lyrics_json_path: str,
    track_name: str,
    username: str,
    original_artist: str,
    output_path: str,
    video_waveform_path: str,
    font_path: str = "/path/to/Ethnocentric.ttf",
    arial_font_path: str = "/path/to/arial_bold.ttf",
):
    # 1) Get audio duration
    duration = ffprobe_duration(audio_path)

    # 2) Load lyrics JSON
    with open(lyrics_json_path, "r") as f:
        lyrics_data = json.load(f)["lyrics"]["data"]

    # 3) Video geometry & positions
    BG_W, BG_H = 1080, 1920
    IMG_SIZE = 240
    USER_X = (BG_W - IMG_SIZE) // 6
    USER_Y = int(BG_H * 0.15) - IMG_SIZE // 2

    LOGO_SIZE = 120
    LOGO_X = USER_X + IMG_SIZE - LOGO_SIZE // 2 - 40
    LOGO_Y = USER_Y + IMG_SIZE - LOGO_SIZE // 2 - 20

    TEXT_X = (BG_W - IMG_SIZE) / 1.7
    TEXT_Y = USER_Y + 40

    fc = []

    # 4a) Black background of same duration
    fc.append(f"color=black:s={BG_W}x{BG_H}:d={duration}[bg]")

    # 4b) Mask user image into a circle (RGBA)
    fc.append(
        f"[1:v]scale={IMG_SIZE}:{IMG_SIZE},format=yuva444p,"
        "geq="
        "lum='p(X,Y)':"
        "a='if(lte(pow(X-(W/2),2)+pow(Y-(H/2),2),pow(min(W,H)/2,2)),255,0)'"
        "[usercircle]"
    )

    # 4c) Overlay the flat JPEG border
    fc.append(f"[2:v]scale={IMG_SIZE + 18}:{IMG_SIZE + 18}[border]")
    fc.append(f"[bg][border]overlay=x={USER_X - 10}:y={USER_Y - 10}[step0]")

    # 4d) Overlay user-circle on top of border
    fc.append(f"[step0][usercircle]overlay=x={USER_X}:y={USER_Y}[step1]")

    # 4e) Scale & overlay brand logo
    fc.append(f"[3:v]scale={LOGO_SIZE}:{LOGO_SIZE}[logo]")
    fc.append(f"[step1][logo]overlay=x={LOGO_X}:y={LOGO_Y}[step2]")

    # 4e2) Add looped video waveform
    waveform_height = int(BG_H * 0.15)
    waveform_y = (BG_H - waveform_height) // 2.25
    fc.append(f"[4:v]scale={BG_W}:{waveform_height}[waveform_scaled]")
    fc.append("[waveform_scaled]loop=loop=-1:size=32767:start=0[waveform]")
    fc.append(
        f"[step2][waveform]overlay=x=0:y={waveform_y}:repeatlast=0[step2_waveform]"
    )

    # 4f) Draw track title, original artist, and username
    fc.append(
        f"[step2_waveform]drawtext="
        f"fontfile='{font_path}':"
        f"text='{track_name}':"
        "fontcolor=#FEFEFECC:"
        "fontsize=40:"
        f"x={TEXT_X}:y={TEXT_Y}"
        "[tt1]"
    )
    fc.append(
        "[tt1]drawtext="
        f"fontfile='{arial_font_path}':"
        f"text='{original_artist.upper()}':"
        "fontcolor=#FEFEFECC:"
        "fontsize=32:"
        f"x={TEXT_X}:y={TEXT_Y + 50}"
        "[tt2]"
    )
    fc.append(
        "[tt2]drawtext="
        f"fontfile='{arial_font_path}':"
        f"text='{username}':"
        "fontcolor=#FEFEFECC:"
        "fontsize=36:"
        f"x={TEXT_X}:y={TEXT_Y + 140}"
        "[step3]"
    )

    # 4g) Draw lyrics (previous, current, next) below waveform
    lyrics_start_y = waveform_y + waveform_height + 250
    block_height = 120
    line_height = 46
    line_height_other = 36
    font_size_current = 42
    font_size_other = 24
    color_current = "#ECAED9DE"
    color_other = "#ECAED961"

    current_label = "step3"
    filter_idx = 0
    n_lyrics = len(lyrics_data)

    for i in range(n_lyrics):
        line_no = lyrics_data[i]["line_number"]
        start_s = (
            parse_time("00:00:000")
            if line_no == 1
            else parse_time(lyrics_data[i]["start_time"])
        )
        end_s = parse_time(lyrics_data[i]["end_time"])

        # Handle edge case: last lyric with end_time 00:00:000
        if i == n_lyrics - 1 and lyrics_data[i]["end_time"] == "00:00:000":
            end_s = min(duration, start_s + 10.0)  # Highlight for 10s or video end

        # Current block
        current_text = lyrics_data[i]["text"]
        current_lines = normalize_lyrics(current_text, max_chars=20)
        for j, line in enumerate(current_lines):
            y_pos = lyrics_start_y + block_height + j * line_height
            filter_idx += 1
            new_label = f"after_dt{filter_idx}"
            fc.append(
                f"[{current_label}]drawtext="
                f"fontfile='{font_path}':"
                f"text='{line}':"
                f"fontcolor={color_current}:"
                f"fontsize={font_size_current}:"
                f"x=(w-text_w)/2:y={y_pos}:"
                f"enable='between(t,{start_s},{end_s})'"
                f"[{new_label}]"
            )
            current_label = new_label

        # Previous block
        if i > 0:
            prev_text = lyrics_data[i - 1]["text"]
            prev_lines = normalize_lyrics(prev_text, max_chars=25)
            for j, line in enumerate(prev_lines):
                # esc_line = line.replace("'", r"\'")
                y_pos = lyrics_start_y + j * line_height_other
                filter_idx += 1
                new_label = f"after_dt{filter_idx}"
                fc.append(
                    f"[{current_label}]drawtext="
                    f"fontfile='{font_path}':"
                    f"text='{line}':"
                    f"fontcolor={color_other}:"
                    f"fontsize={font_size_other}:"
                    f"x=(w-text_w)/2:y={y_pos}:"
                    f"enable='between(t,{start_s},{end_s})'"
                    f"[{new_label}]"
                )
                current_label = new_label

        # Next block
        if i < n_lyrics - 1:
            next_text = lyrics_data[i + 1]["text"]
            next_lines = normalize_lyrics(next_text, max_chars=25)
            for j, line in enumerate(next_lines):
                # esc_line = line.replace("'", r"\'")
                y_pos = lyrics_start_y + 2 * 140 + j * line_height_other
                filter_idx += 1
                new_label = f"after_dt{filter_idx}"
                fc.append(
                    f"[{current_label}]drawtext="
                    f"fontfile='{font_path}':"
                    f"text='{line}':"
                    f"fontcolor={color_other}:"
                    f"fontsize={font_size_other}:"
                    f"x=(w-text_w)/2:y={y_pos}:"
                    f"enable='between(t,{start_s},{end_s})'"
                    f"[{new_label}]"
                )
                current_label = new_label

    last_label = current_label

    # 5) Build & run the ffmpeg command
    cmd = [
        "ffmpeg",
        "-y",
        "-f",
        "lavfi",
        "-i",
        "anullsrc=channel_layout=stereo:sample_rate=44100",
        "-i",
        user_image_path,
        "-i",
        border_path,
        "-i",
        brand_logo_path,
        "-i",
        video_waveform_path,
        "-i",
        audio_path,
        "-filter_complex",
        ";".join(fc),
        "-map",
        f"[{last_label}]",
        "-map",
        "5:a",
        "-c:v",
        "libx264",
        "-preset",
        "ultrafast",
        "-crf",
        "40",
        "-pix_fmt",
        "yuv420p",
        "-c:a",
        "aac",
        "-b:a",
        "64k",
        output_path,
    ]

    print("Running ffmpeg:\n", " ".join(shlex.quote(x) for x in cmd))
    subprocess.run(cmd, check=True)


if __name__ == "__main__":
    cwd = os.getcwd()
    audio_path = f"{cwd}/assets/faded_Symphony_90.flac"
    user_image_path = f"{cwd}/assets/profile.webp"
    border_path = f"{cwd}/assets/profile_border.png"
    brand_logo_path = f"{cwd}/assets/full_jacket_pink_200.png"
    lyrics_json_path = f"{cwd}/assets/faded_Symphony_90.json"
    video_waveform_path = f"{cwd}/assets/wave_spectrum.mp4"
    output_path = f"{cwd}/outputs/faded_lyrics_video.mp4"
    create_lyric_video(
        audio_path=audio_path,
        user_image_path=user_image_path,
        border_path=border_path,
        brand_logo_path=brand_logo_path,
        lyrics_json_path=lyrics_json_path,
        video_waveform_path=video_waveform_path,
        track_name="Faded",
        username="Animesh",
        original_artist="Alan Walker",
        output_path=output_path,
        font_path=f"{cwd}/assets/ethnocentric_rg.otf",
        arial_font_path=f"{cwd}/assets/ethnocentric_rg_it.otf",
    )

    # Special handling for the first lyric to display from t=0
    # if n_lyrics > 0:
    #     first_text = lyrics_data[0]["text"]
    #     first_start_s = parse_time(lyrics_data[0]["start_time"])
    #     first_lines = normalize_lyrics(first_text, max_chars=20)
    #     for j, line in enumerate(first_lines):
    #         # esc_line = line.replace("'", r"\'")
    #         # First line: same font size as current, white color
    #         if j == 0:
    #             y_pos = lyrics_start_y + block_height
    #             font_size = font_size_current
    #         # Second line (if exists): next line style
    #         else:
    #             y_pos = lyrics_start_y + block_height + line_height
    #             font_size = font_size_current
    #         filter_idx += 1
    #         new_label = f"after_dt{filter_idx}"
    #         fc.append(
    #             f"[{current_label}]drawtext="
    #             f"fontfile='{font_path}':"
    #             f"text='{line}':"
    #             f"fontcolor={color_other}:"
    #             f"fontsize={font_size}:"
    #             f"x=(w-text_w)/2:y={y_pos}:"
    #             f"enable='between(t,0,{first_start_s})'"
    #             f"[{new_label}]"
    #         )
    #         current_label = new_label
