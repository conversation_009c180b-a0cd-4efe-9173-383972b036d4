import subprocess

def formator(video_path, output_path):
    subprocess.run([
    "ffmpeg",
    "-i", video_path,
    "-c:v", "libx264",
    "-preset", "fast",
    "-crf", "18",
    "-an",
    output_path
])

if __name__ == "__main__":
    video_path = "/Users/<USER>/Downloads/YT/Skyfall___James_<PERSON>___Daniel_Craig___4K___Theme_Song___Adele_4fb3f3a9.mp4"
    output_path = "/Users/<USER>/Downloads/YT/Skyfall.mp4"
    formator(video_path, output_path)