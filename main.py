import subprocess
import os
# import datetime
from pydub import AudioSegment
import time
# import json
# import shlex



def get_audio_duration(audio_path):
    audio = AudioSegment.from_file(audio_path)
    return audio.duration_seconds

# def get_media_file_metadata(input):
#     command = [
#         "ffmpeg",
#         "-i", input
#     ]
#     subprocess.run(command, check=True)
#     return

# def resize_image_resolution(input_image:str, output_image:str):
#     command = [
#         "ffmpeg" ,
#        "-i", input_image, 
#         "-vf", "scale=1080:1920" ,
#         output_image
#     ]
#     subprocess.run(command, check=True)
#     return

# def convert_audio_bitrate(input_audio:str, output_audio:str):
#     command = [
#         "ffmpeg" ,
#         "-i", input_audio, 
#         "-c:a", "aac",  
#         "-b:a", "192k",
#         output_audio
#     ]
#     subprocess.run(command, check=True)
#     return

# def create_temp_video(image_file, audio_duration, temp_video_file):
#     ffmpeg_command = [
#         "ffmpeg",
#         "-loop", "1",                    # Loop the image
#         "-i", image_file,                # Input image
#         "-t", str(audio_duration),       # Set the duration to match the audio
#         "-vf", "scale=1080:1920",        # Set the resolution
#         "-pix_fmt", "yuv420p",           # Set pixel format
#         "-c:v", "libx264",               # Video codec
#         "-preset", "ultrafast",          # Use the fastest preset
#         temp_video_file                  # Output temporary video file
#     ]
#     subprocess.run(ffmpeg_command, check=True)
#     return

# def simple_image_bg_video(audio_path:str, image_path:str,  output_path):

#     command  =  [
#         "ffmpeg",
#         "-loop", "1",                   # Loop the image
#         "-i", image_path,               # Input image
#         "-i", audio_path,               # Input audio
#         "-c:v", "libx264", 
#         "-preset", "ultrafast",
#         "-c:a", "aac",                  # Audio codec
#         "-b:a", "192k",                 # Audio bitrate
#         # "-vf", "scale=1080:1920",       # Set the resolution
#         "-vf", (
#         "scale=1080:1920,drawtext=text='Should\\\\\\\\\\\\'ve gave you all my hours':fontcolor=white:fontsize=48:x=(w-text_w)/2:y=(h-text_h)/2:enable='between(t,10.5,12.2)'"  # Show text between 10 and 20 seconds
#     ),
#         "-shortest",                    # Match video length to audio length
#         # "-pix_fmt", "yuv420p",  
#         output_path                     # Output file
#     ]


#     subprocess.run(command, check=True)
#     print("✅ completed")
#     return


# def simple_image_bg_video_lyrics(audio_path:str, image_path:str, lyrics,  output_path):
#     def timestamp_to_seconds(timestamp):
#         m, s, ms = map(int, timestamp.split(':'))
#         return f"{(m * 60) + s}.{ms}"
# # Generate drawtext filters
#     drawtext_filters = []
#     for entry in lyrics:
#         start_time = timestamp_to_seconds(entry['start_time'])
#         end_time = timestamp_to_seconds(entry['end_time'])
#         # text = shlex.quote(entry['text'])
#         text = entry['text'].replace("'", "\\'")  # Escape single quotes in the text
#         text = text.replace('"', '\\"')
#         text_filter = (
#             f"drawtext=text={text}:"
#             f"fontcolor=white:fontsize=48:x=(w-text_w)/2:y=(h-text_h)/2:"
#             f"enable='between(t,{start_time},{end_time})'"
#         )
#         drawtext_filters.append(text_filter)

#     combined_filters = ",".join(drawtext_filters)
#     print(f"combined_filters: {combined_filters}")

#     command  =  [
#         "ffmpeg",
#         "-loop", "1",                   # Loop the image
#         "-i", image_path,               # Input image
#         "-i", audio_path,               # Input audio
#         "-c:v", "libx264", 
#         "-preset", "ultrafast",
#         "-c:a", "aac",                  # Audio codec
#         "-b:a", "192k",                 # Audio bitrate
#         "-vf", f"scale=1080:1920,{combined_filters}",
#         "-shortest",                    # Match video length to audio length
#         # "-pix_fmt", "yuv420p",  
#         output_path                     # Output file
#     ]
#     subprocess.run(command, check=True)
#     print("✅ completed")
#     return


# def create_temp_video_with_loop_video(audio_file_path):
#     op =  os.path.join(os.getcwd(), "videos", f"{time.time_ns()}.mp4" )
#     audio_duration = get_audio_duration(audio_file_path)

#     ffmpeg_command = [
#         "ffmpeg",
#         "-loop", "1",                    # Loop the image
#         "-i",  "/Users/<USER>/ffmpeg-video-experiments/videos/cat_animation.mp4",  
#         "-i", audio_file_path,               # Input audio
#         "-t", str(audio_duration),       # Set the duration to match the audio
#         "-shortest", op                 # Output temporary video file
#     ]
#     subprocess.run(ffmpeg_command, check=True)
#     return


def loop_video_with_audio(video_file, audio_file, output_file):
    audio_duration = str(get_audio_duration(audio_file))
    
    ffmpeg_command = [
        'ffmpeg', '-y', 
        '-stream_loop', '-1', 
        '-i', video_file,
        '-i', audio_file,
        '-map', '0:v',
        '-map', '1:a',
        '-c:v', 'libx264', 
        '-preset', 'veryfast', 
        '-tune', 'film',
        '-c:a', 'aac', 
        '-b:a', '192k', 
        '-shortest',
        '-t', audio_duration, 
        output_file
    ]
    
    subprocess.run(ffmpeg_command, check=True)





if  __name__ == "__main__":
    # video_file =  os.path.join(os.getcwd(), "videos", "sample_1.mp4")
    audio_files = os.listdir(os.path.join(os.getcwd(), "English"))
    
    for audio_file_name in audio_files:
        output_file_name =  audio_file_name.replace(".mp3", ".mp4")
        video_file =  os.path.join(os.getcwd(), "videos", "cat.mp4")
        output_file = os.path.join(os.getcwd(), "video_outputs", output_file_name)
        audio_file = os.path.join(os.getcwd(), "English", audio_file_name)

        loop_video_with_audio(video_file, audio_file, output_file)

    # print("Hello! main")
    # song = "when_i_was_your_man"
    # scale = "C"
    # tempo = "75"
    # genre = "rock"

    # img_path = os.path.join(os.getcwd(), "images", genre, "_sample_1.jpg")
    # # rs_path = os.path.join(os.getcwd(), "images", genre, "_sample_1.jpg")
    # aud_path = os.path.join(os.getcwd(), "audios", f"{song}_{scale}.mp3")
    # # cv_aud_path  = os.path.join(os.getcwd(), "audios", f"_{song}_{scale}.aac")
    # op_path =  os.path.join(os.getcwd(), "outputs", f"{song}_{scale}_{tempo}_{datetime.datetime.now()}.mp4")
    # # temp_vd_path  = os.path.join(os.getcwd(), "outputs", f"temp_{song}_{scale}_{tempo}_{datetime.datetime.now()}.mp4")
    # lyrics_path = os.path.join(os.getcwd(), "lyrics", f"{song}_{tempo}.json")

    # start_time = time.time()

    # audio_duration = get_audio_duration(aud_path)
    # create_temp_video(img_path, audio_duration, temp_vd_path)

    # simple_image_bg_video(audio_path=aud_path, image_path=img_path, output_path=op_path)

    # resize_image_resolution(img_path, rs_path)
    # get_media_file_metadata("/Users/<USER>/ffmpeg-video-experiments/outputs/when_i_was_your_man_C_75_2024-07-07 09:55:08.939474.mp4")
    # convert_audio_bitrate(aud_path, cv_aud_path)

    # with open(lyrics_path, 'r') as file:
    #     json_data = json.load(file)
    #     lyrics = json_data["lyrics"]["data"]
    #     # print(lyrics)
    #     simple_image_bg_video_lyrics(audio_path=aud_path,image_path=img_path, lyrics=lyrics, output_path=op_path)


    # end_time = time.time()
    # time_taken = end_time - start_time

    # print(f"Time taken: {time_taken:.2f} seconds")
    # Example usage

    # create_temp_video_with_loop_video(ip_audio)