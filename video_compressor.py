import os
import subprocess
import time


def compress(input_file: str, output_file: str):
    ffmpeg_command = [
    "ffmpeg",
    "-i", input_file,  # Input file
    "-vcodec", "libx264",
    "-crf", "24",  # Lower CRF = higher quality (range: 0-51, sane 18-28)
    "-preset", "slower",  # Better compression efficiency (slow/veryslow)
    "-tune", "film",  # Optimize for video content (options: film, animation, etc.)
    "-acodec", "aac",
    "-b:a", "192k",
    "-movflags", "+faststart",  # Enable streaming-friendly output
    output_file,  # Output file
]
    subprocess.run(ffmpeg_command, check=True)

def get_media_file_metadata(input_file):
    command = [
        "ffmpeg",
        "-i", input_file
    ]
    subprocess.run(command, check=True)
    return

def get_media_file_metadata2(input_file):
    # ffprobe -i your_video.mp4 
    command = [
        "ffprobe",
        "-i", input_file
    ]
    subprocess.run(command, check=True)
    return


def video_hls(input_file:str, output_file:str):
    cmd = f"ffmpeg -i {input_file} -codec: copy -start_number 0 -hls_time 10 -hls_list_size 0 -f hls {output_file}"
    subprocess.run(cmd, shell=True)


if __name__ == "__main__":
    input = "/Users/<USER>/animesh-melodyze/ffmpeg-experiments/videos/in_1737725303.mp4"
    output = f"/Users/<USER>/animesh-melodyze/ffmpeg-experiments/outputs/{int(time.time())}.mp4"
    compress(input, output)
    file_size = os.path.getsize(output) / (1024 * 1024)
    print(f"File size: {file_size} MB")
    
    # hls_output = f"/Users/<USER>/animesh-melodyze/ffmpeg-experiments/outputs/hls_{int(time.time())}.m3u8"
    # video_hls(input, hls_output)
    # get_media_file_metadata(input)

    
